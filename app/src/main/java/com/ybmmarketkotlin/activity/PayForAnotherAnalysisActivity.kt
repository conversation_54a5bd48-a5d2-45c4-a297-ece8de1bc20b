package com.ybmmarketkotlin.activity

import android.os.Bundle
import com.ybmmarket20.bean.SearchResultOPBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.xyyreport.page.payResult.PayResultAnotherReport
import com.ybmmarket20.xyyreport.page.search.SearchReportConstant

abstract class PayForAnotherAnalysisActivity: BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        trackPv()
    }

    fun trackPv() {
        val orderNo = intent.getStringExtra("orderNo")
        PayResultAnotherReport.pvTrack(this, orderNo)
    }

    fun addGoodsReportParams(searchResult: SearchResultOPBean) {
        searchResult.rows?.forEach {
            it.productInfo?.mIsPayResult = true
            it.productInfo?.qtListData = searchResult.qtListData
            it.productInfo?.listExpId = searchResult.expId
            it.productInfo?.searchRecPurchaseStrategyCode = searchResult.expId
            it.productInfo?.scmId = searchResult.scmId
        }
        putExtension(SearchReportConstant.EXTENSION_SEARCH_GOODS_SCM_ID, searchResult.scmId)
    }

}