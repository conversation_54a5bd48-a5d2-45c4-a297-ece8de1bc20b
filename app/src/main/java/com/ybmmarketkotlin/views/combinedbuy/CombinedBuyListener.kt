package com.ybmmarketkotlin.views.combinedbuy

import com.ybmmarket20.bean.GroupPurchaseInfo
import com.ybmmarket20.bean.RowsBeanCombinedExt

/**
 * <AUTHOR>
 * @desc 组合购、加价购 回调
 */
interface CombinedBuyListener {
    /**
     * [curSubPosition] 组合购当前选中的副品位置
     */
    fun preSettle(subPosition:Int = -1){

    }

    /**
     * [bean] 当前数据变更实体
     * [curSubPosition] 组合购当前选中的副品位置
     * [addFlag] 是否为增加
     */
    fun changeNum(bean:RowsBeanCombinedExt,curSubPosition:Int = -1,addFlag:Boolean = true,preNum:Int = -1){

    }

    /**
     * [bean] 当前数据变更实体
     * [curSubPosition] 组合购当前选中的副品位置
     * [preNum] 数量修改框变更前的数字
     */
    fun changeNumClick(bean:RowsBeanCombinedExt,curSubPosition:Int = -1,addFlag:Boolean = true,preNum:Int = -1){

    }
    fun jumpToGoodsDetail(bean:RowsBeanCombinedExt){
    }
    fun refresh(preItem:RowsBeanCombinedExt,preSubPosition:Int,item:RowsBeanCombinedExt,subPosition:Int,firstFlag:Boolean){

    }

    fun pageSeleted(position:Int,item: GroupPurchaseInfo){

    }

    // 横向滚动
    fun scrollHorinal(scrollPosition:Int,scrollOffset:Int){

    }

    /**
     * @butonType   0:加购(+ 普通、秒杀) 1:拼团 2：批购包邮
     * @buttonText  "加购" "参团" "抢购"
     */
    fun btnClickTrack(buttonType:Int,buttonText:String,position:Int){

    }

    fun startBanner(){

    }
    fun stopBanner(){

    }
}