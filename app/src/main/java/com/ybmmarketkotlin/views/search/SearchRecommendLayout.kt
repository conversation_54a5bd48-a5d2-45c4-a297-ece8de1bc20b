package com.ybmmarketkotlin.views.search

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.R
import com.ybmmarket20.bean.GoodsSearchRecommendInfo
import com.ybmmarket20.bean.RowsBeanCombinedExt
import com.ybmmarketkotlin.adapter.GoodsSearchRecommendItemAdapter
import com.ybmmarketkotlin.views.combinedbuy.CombinedBuyListener

/**
 * <AUTHOR>
 * @desc    大搜-推荐区域
 * @date    2025/8/18
 */
class SearchRecommendLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, def: Int = 0) :
    ConstraintLayout(context, attrs, def) {
    val adapter by lazy {
        GoodsSearchRecommendItemAdapter(mutableListOf())
    }
    private val rlvGoods by lazy { findViewById<RecyclerView>(R.id.rlvGoods) }
    private val tvTitle by lazy { findViewById<TextView>(R.id.tvTitle) }
    var mListener: CombinedBuyListener? = null
        set(value) {
            field = value
            adapter.mListener = object : CombinedBuyListener {
                override fun jumpToGoodsDetail(bean: RowsBeanCombinedExt) {
                    field?.jumpToGoodsDetail(bean)
                }

                override fun btnClickTrack(buttonType: Int, buttonText: String, position: Int) {
                    field?.btnClickTrack(buttonType, buttonText, position)
                }
            }
            adapter.mListener = value
        }

    private var mRecommendInfo: GoodsSearchRecommendInfo? = null

    init {
        LayoutInflater.from(context).inflate(R.layout.layout_search_recommend_area, this)
        rlvGoods.adapter = adapter
        setupScrollListener()
    }

    /**
     * 横向listView滚动监听
     */
    private fun setupScrollListener() {
        rlvGoods.clearOnScrollListeners()
        rlvGoods.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    updateScrollPosition()
                }
            }

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                // 实时更新位置，确保状态准确
                updateScrollPosition()
            }
        })
    }

    /**
     * 记录滚动下标跟偏移量
     */
    private fun updateScrollPosition() {
        val layoutManager = rlvGoods.layoutManager as LinearLayoutManager
        val scrollPosition = layoutManager.findFirstVisibleItemPosition()

        // 保存精确的偏移量
        val firstVisibleView = layoutManager.findViewByPosition(scrollPosition)
        var scrollOffset = 0
        if (firstVisibleView != null) {
            scrollOffset = firstVisibleView.left - layoutManager.paddingLeft
        }
        mListener?.scrollHorinal(scrollPosition,scrollOffset)
    }

    /**
     * 设置商品
     */
    fun setNewData(bean: GoodsSearchRecommendInfo,scrollPosition:Int,scrollOffset:Int) {
        mRecommendInfo = bean
        tvTitle.text = mRecommendInfo?.title
        adapter.setNewData(mRecommendInfo!!.products)
        val layoutManager = rlvGoods.layoutManager as LinearLayoutManager
        layoutManager.scrollToPositionWithOffset(scrollPosition,scrollOffset)
    }
}