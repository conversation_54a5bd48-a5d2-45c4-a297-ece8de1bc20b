package com.ybmmarketkotlin.views

import android.content.Context
import android.graphics.Color
import android.os.CountDownTimer
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.tencent.tddiag.logger.TDLog
import com.ybmmarket20.R
import com.ybmmarket20.bean.CouponsTipsResponse
import com.ybmmarket20.bean.ShoppingGoldRechargeBean
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarketkotlin.bean.BannerType
import com.ybmmarketkotlin.bean.OrderTopBannerBean
import com.ybmmarketkotlin.utils.TimeUtils

class OrderTopBannerLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    def: Int = 0
) :
    ConstraintLayout(context, attrs, def) {
    private val rebateView by lazy { findViewById<ConstraintLayout>(R.id.include_oder_rebate_view) }
    private val couponView by lazy { findViewById<ConstraintLayout>(R.id.include_order_coupon_view) }
    private val goldView by lazy { findViewById<ConstraintLayout>(R.id.include_order_red_pack_view) }
    private val aptitudeView by lazy { findViewById<ConstraintLayout>(R.id.include_order_aptitude_overdue_view) }
    private val imageView by lazy { findViewById<ImageView>(R.id.iv_image_view) }

    private var mCountDownTimer: CountDownTimer? = null

    private var bannerClickCallBack: OnBannerClickCallBack? = null

    interface OnBannerClickCallBack {
        fun onViewClicked(type: BannerType, item: OrderTopBannerBean?, couponClaimed: Int?=0, couponId: String?="", h5Url: String?="")
        fun onBannerExposure(type: BannerType)
    }
    fun setBannerClickCallBack(listener: OnBannerClickCallBack?) {
        this.bannerClickCallBack = listener
    }
    init {
        LayoutInflater.from(context).inflate(R.layout.layout_order_top_banner, this)
        initView()
    }

    private fun initView() {

    }

    fun setNewData(bean: OrderTopBannerBean) {
        updateTotalData(bean)
    }

    private fun updateTotalData(dataBean: OrderTopBannerBean) {
        imageView.visibility = GONE
        rebateView.visibility = GONE
        couponView.visibility = GONE
        goldView.visibility = GONE
        aptitudeView.visibility = GONE
        when (dataBean.type) {
            BannerType.IMAGE -> {
                if (!dataBean.saasAd?.data?.saasImageUrl.isNullOrEmpty()) {
                    imageView.visibility = VISIBLE
                    var imageUrl = ""
                    if (dataBean.saasAd?.data?.saasImageUrl?.startsWith("http") == true ||
                        dataBean.saasAd?.data?.saasImageUrl?.startsWith("Http") == true
                    ) {
                        imageUrl = dataBean.saasAd.data.saasImageUrl
                    } else {
                        imageUrl =
                            AppNetConfig.getCDNHost() + (dataBean.saasAd?.data?.saasImageUrl ?: "")
                    }
                    Glide.with(context)
                        .load(imageUrl)
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .into(imageView)
                    imageView.setOnClickListener {
                        bannerClickCallBack?.onViewClicked(
                           type= dataBean.type,
                           item = dataBean
                        )
                    }
                }
            }
            BannerType.CUSTOM_VIEW_COUPON -> {
                couponView.visibility = VISIBLE
                val tvCoupon = couponView.findViewById<TextView>(R.id.tv_coupon)
                dataBean.sceneResponse?.data?.let {
                    setCouponDate(it, tvCoupon, couponView)
                }
            }

            BannerType.CUSTOM_VIEW_REBATE -> {
                rebateView.visibility = VISIBLE
                val tvTime = rebateView.findViewById<TextView>(R.id.tv_countdown_time)
                val tvTips = rebateView.findViewById<TextView>(R.id.tv_tips)
                dataBean.consumeRebateDetail?.let {
                    it.data?.toEtimeSeconds?.let { it1 -> setCountDownTime(it1, tvTime) }
                    when (it.data?.levelScene) {
                        "1" -> setRebateAmountTypeNot(it.data.lastMonthExpectedAmount, tvTips)
                        "2" -> setRebateAmountTypeNone(
                            it.data.nextLevelShortAmount,
                            it.data.nextLevelRate,
                            tvTips
                        )

                        "3" -> setRebateAmountTypeAlready(
                            it.data.realAmount,
                            it.data.nextLevelShortAmount,
                            it.data.nextLevelRedPacketAmount,
                            tvTips
                        )

                        "4" -> setRebateAmountTypeMax(
                            it.data.realAmount,
                            it.data.actTags?.maxReturnRedPackageAmount,
                            tvTips
                        )
                    }
                    rebateView.setOnClickListener {
                        bannerClickCallBack?.onViewClicked(type=dataBean.type, item = dataBean)
                    }
                }
            }

            BannerType.CUSTOM_VIEW_APTITUDE -> {
                aptitudeView.visibility = VISIBLE
                val tvTips = aptitudeView.findViewById<TextView>(R.id.tv_aptitude)
                dataBean.licenseRemind?.data?.msg?.let {
                    tvTips.text = it
                }
                aptitudeView.setOnClickListener {
                    bannerClickCallBack?.onViewClicked(type=dataBean.type, item=dataBean)
                }
            }

           BannerType.CUSTOM_VIEW_RED_PACK -> {
                goldView.visibility = VISIBLE
                val tvRedPackTips = goldView.findViewById<TextView>(R.id.tv_red_pack_tips)
                dataBean.rechargeDiscount?.data?.let {
                    setRedPackDate(it, tvRedPackTips, goldView)
                    goldView.setOnClickListener {
                        bannerClickCallBack?.onViewClicked(type=dataBean.type, item=dataBean)
                    }
                }
            }

            else -> {}
        }
    }

    private fun setCouponDate(couponDate: CouponsTipsResponse, tvCoupon: TextView, itemView: View) {
        var tipsContent = ""
        var mUrl = ""
        var couponClaimed = -1
        var couponId = ""
        val spannableStringBuilder = SpannableStringBuilder()
        couponDate.scenes?.forEachIndexed { index, scene ->
            if (index == 0) {
                scene.coupons?.forEachIndexed { idx, coupon ->
                    if (idx == 0) {
                        couponId = coupon.couponId ?: ""
                        tipsContent = coupon.couponText ?: ""
                        mUrl = coupon.hrefUrl ?: ""
                        couponClaimed = coupon.claimStatus ?: -1
                        coupon.sceneGroupId = scene.sceneGroupId
                    }
                }
            }
        }

        val tipsStringBuilder = SpannableStringBuilder().apply {
            append(tipsContent)
            setSpan(
                ForegroundColorSpan(ContextCompat.getColor(context, R.color.color_F70015)),
                0,
                tipsContent.length,
                0
            )
        }

        spannableStringBuilder.append("送您一张").append(tipsStringBuilder)
        tvCoupon.text = spannableStringBuilder
        itemView.setOnClickListener {
            bannerClickCallBack?.onViewClicked(type=BannerType.CUSTOM_VIEW_COUPON,item=null,couponClaimed=couponClaimed,couponId=couponId, h5Url  =mUrl)
        }
    }

    /**
     * 设置购物金红包数据
     */
    private fun setRedPackDate(
        redPackDate: ShoppingGoldRechargeBean,
        tvTips: TextView,
        itemView: View
    ) {
        val upperHalf = redPackDate.highLevelRedPacketMsgUpperHalf ?: ""
        val lowerHalf = redPackDate.highLevelRedPacketMsgLowerHalf ?: ""

        val spannableStringBuilder = SpannableStringBuilder().apply {
            if (upperHalf.isNotEmpty()) {
                append(upperHalf)
                setSpan(ForegroundColorSpan(Color.BLACK), 0, upperHalf.length, 0)
            }
            if (lowerHalf.isNotEmpty()) {
                append("\n" + lowerHalf)
                setSpan(
                    ForegroundColorSpan(ContextCompat.getColor(context, R.color.color_F70015)),
                    upperHalf.length,
                    upperHalf.length + lowerHalf.length + 1,
                    0
                )
            }
        }
        tvTips.text = spannableStringBuilder
    }

    /**
     * 返利类型1：未参与
     */
    private fun setRebateAmountTypeNot(amount: String?, tvTips: TextView) {
        val baseText = "参与下单返活动，预估月均可返红包${amount}元"
        val spannable = SpannableString(baseText)
        val amountStart = baseText.indexOf(amount ?: "")
        if (amountStart >= 0 && !amount.isNullOrEmpty()) {
            spannable.setSpan(
                ForegroundColorSpan(Color.parseColor("#FF0000")),
                amountStart,
                amountStart + amount.length + 1,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        tvTips.text = spannable
    }

    /**
     * 返利类型2：未达标
     */
    private fun setRebateAmountTypeNone(
        nextLevelShortAmount: String?,
        nextLevelRate: String?,
        tvTips: TextView
    ) {
        val baseText = "仅差${nextLevelShortAmount}元参与返利活动，返利${nextLevelRate}%起"
        val spannable = SpannableString(baseText)
        val amountStart = baseText.indexOf(nextLevelShortAmount ?: "")
        if (amountStart >= 0 && !nextLevelShortAmount.isNullOrEmpty()) {
            spannable.setSpan(
                ForegroundColorSpan(Color.parseColor("#FF0000")),
                amountStart,
                amountStart + nextLevelShortAmount.length + 1,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        val rateStart = baseText.indexOf(nextLevelRate ?: "")
        if (rateStart >= 0 && !nextLevelRate.isNullOrEmpty()) {
            spannable.setSpan(
                ForegroundColorSpan(Color.parseColor("#FF0000")),
                rateStart,
                rateStart + nextLevelRate.length + 1,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        tvTips.text = spannable
    }

    /**
     * 返利类型3：已达标
     */
    private fun setRebateAmountTypeAlready(
        realAmount: String?,
        nextLevelShortAmount: String?,
        nextLevelRedPacketAmount: String?,
        tvTips: TextView
    ) {
        var baseText = ""
        val nextLevelShortAmountInt = try {
            nextLevelShortAmount?.toDouble() ?: 0.0
        } catch (e: Exception) {
            0.0
        }
        baseText = if (nextLevelShortAmountInt > 0) {
            "已获得红包${realAmount}元，仅差${nextLevelShortAmount}元，可返红包${nextLevelRedPacketAmount}元"
        } else {
            "已获得红包${realAmount}元"
        }
        val spannable = SpannableString(baseText)
        val amountStart = baseText.indexOf(nextLevelShortAmount ?: "")
        if (amountStart >= 0 && !nextLevelShortAmount.isNullOrEmpty()) {
            spannable.setSpan(
                ForegroundColorSpan(Color.parseColor("#FF0000")),
                amountStart,
                amountStart + nextLevelShortAmount.length + 1,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        val rateStart = baseText.indexOf(nextLevelRedPacketAmount ?: "")
        if (rateStart >= 0 && !nextLevelRedPacketAmount.isNullOrEmpty()) {
            spannable.setSpan(
                ForegroundColorSpan(Color.parseColor("#FF0000")),
                rateStart,
                rateStart + nextLevelRedPacketAmount.length + 1,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        tvTips.text = spannable
    }

    /**
     * 返利类型4：最高级
     */
    private fun setRebateAmountTypeMax(
        realAmount: String?,
        maxReturnRedPackageAmount: String?,
        tvTips: TextView
    ) {
        val baseText = "已获得红包${realAmount}元，多买多返最高返${maxReturnRedPackageAmount}元"
        val spannable = SpannableString(baseText)
        val amountStart = baseText.indexOf(realAmount ?: "")
        if (amountStart >= 0 && !realAmount.isNullOrEmpty()) {
            spannable.setSpan(
                ForegroundColorSpan(Color.parseColor("#FF0000")),
                amountStart,
                amountStart + realAmount.length + 1,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        val rateStart = baseText.indexOf(maxReturnRedPackageAmount ?: "")
        if (rateStart >= 0 && !maxReturnRedPackageAmount.isNullOrEmpty()) {
            spannable.setSpan(
                ForegroundColorSpan(Color.parseColor("#FF0000")),
                rateStart,
                rateStart + maxReturnRedPackageAmount.length + 1,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        tvTips.text = spannable
    }

    /**
     * 设置倒计时
     */
    private fun setCountDownTime(mSurplusTime: Long, tv_countdown_time: TextView) {
        mCountDownTimer?.cancel()
        mCountDownTimer = object : CountDownTimer(mSurplusTime * 1000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                tv_countdown_time.text = TimeUtils.timeFormat(millisUntilFinished) + "活动结束"
            }

            override fun onFinish() {
                tv_countdown_time.text = "已结束"
            }
        }.start()
    }
}