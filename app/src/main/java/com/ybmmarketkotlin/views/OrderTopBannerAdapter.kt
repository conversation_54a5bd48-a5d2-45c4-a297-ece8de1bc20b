package com.ybmmarketkotlin.views

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.R
import com.ybmmarketkotlin.bean.OrderTopBannerBean
import com.ybmmarketkotlin.views.OrderTopBannerAdapter.OrderTopBannerHolder
import com.ybmmarketkotlin.views.OrderTopBannerLayout.OnBannerClickCallBack
import com.youth.banner.adapter.BannerAdapter

class OrderTopBannerAdapter(val mContext: Context, var dataList: List<OrderTopBannerBean>) :
    BannerAdapter<OrderTopBannerBean, OrderTopBannerHolder>(dataList) {

    var curPageIndex = 0    // 当前第几页
    var banner: BannerOrderTopView? = null
    private var bannerClickCallBack: OnBannerClickCallBack? = null
    fun setBannerClickCallBack(listener: OnBannerClickCallBack?) {
        this.bannerClickCallBack = listener
    }

    override fun onCreateHolder(parent: ViewGroup, viewType: Int): OrderTopBannerHolder =
        OrderTopBannerHolder(
            LayoutInflater.from(mContext).inflate(R.layout.banner_order_top, parent, false)
        )

    override fun onBindView(
        holder: OrderTopBannerHolder,
        data: OrderTopBannerBean,
        position: Int,
        size: Int
    ) {
        val orderTopLayout = holder.itemView.findViewById<OrderTopBannerLayout>(R.id.layout)
        orderTopLayout.setNewData(data)
        orderTopLayout.setBannerClickCallBack(bannerClickCallBack)
    }


    override fun onBindViewHolder(
        holder: OrderTopBannerHolder, position: Int,
        payloads: MutableList<Any>
    ) {
        if (payloads.isNullOrEmpty()) {
            super.onBindViewHolder(holder, position, payloads)
        } else {
            payloads.forEach { it ->
                val data = it as OrderTopBannerBean
                banner?.isAutoLoop(dataList.size > 1)
                holder.itemView.findViewById<OrderTopBannerLayout>(R.id.layout).apply {
                    setNewData(data)
                }
            }
        }
    }

    class OrderTopBannerHolder(itemView: View) : RecyclerView.ViewHolder(itemView)
}