package com.ybmmarketkotlin.views.virtualgold

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.CheckBox
import android.widget.CompoundButton
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import butterknife.Bind
import butterknife.ButterKnife
import com.ybmmarket20.R
import com.ybmmarket20.adapter.ShoppingGoldRechargeAdapter
import com.ybmmarket20.bean.VirtualGoldPayment
import com.ybmmarket20.bean.payment.VirtualGoldRechargeBean

/**
 * <AUTHOR>
 * @desc    收银台：购物金模块
 */
class PayVirtualGoldView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    def: Int = 0
) :
    ConstraintLayout(context, attrs, def) {

    @Bind(R.id.tv_virtual_money_tips)
    lateinit var tvVirtualMoneyTips: TextView

    @Bind(R.id.rv_shopping_gold)
    lateinit var rvShoppingGold: RecyclerView


    @Bind(R.id.cb_virtual_money_on_off)
    lateinit var cbVirtualMoneyOnOff: CheckBox

    private var mData: VirtualGoldPayment? = null
    private var shoppingGoldRechargeAdapter: ShoppingGoldRechargeAdapter? = null
    private var payVirtualGoldListener: OnPayVirtualGoldListener? = null

    interface OnPayVirtualGoldListener {
        fun onRefreshDateCallBack() // 刷新数据回调
        fun onSetCurrentRechargeItem(bean: VirtualGoldRechargeBean?) // 设置当前充值项回调
    }

    fun setPayVirtualGoldListener(listener: OnPayVirtualGoldListener?) {
        this.payVirtualGoldListener = listener
    }

    init {
        LayoutInflater.from(context).inflate(R.layout.layout_pay_virtual_gold, this, true)
        ButterKnife.bind(this)
    }

    fun setData(data: VirtualGoldPayment?) {
        mData = data
        if (mData != null && !TextUtils.isEmpty(data?.virtualGoldTips)) {
            this.isVisible = true
            cbVirtualMoneyOnOff.isChecked = 1 == mData!!.isSelected
            tvVirtualMoneyTips.text = mData!!.virtualGoldTips
            cbVirtualMoneyOnOff.setOnCheckedChangeListener { compoundButton: CompoundButton?, isChecked: Boolean ->
                //如果取消勾选使用购物金  把当前充值选项置为 null
                if (!isChecked){
                    payVirtualGoldListener?.onSetCurrentRechargeItem(null)
                }
                payVirtualGoldListener?.onRefreshDateCallBack()
            }
            initList()
        } else {
            this.isVisible = false
        }
    }

    private fun initList() {
        val virtualGoldRechargeList = mData?.rechargeList
        if (virtualGoldRechargeList.isNullOrEmpty()) {
            rvShoppingGold.isVisible = false
            rvShoppingGold.setAdapter(null)
        } else {
            rvShoppingGold.isVisible = true
            for (bean in virtualGoldRechargeList) {
                if (bean.isSelected()) {
                    payVirtualGoldListener?.onSetCurrentRechargeItem(bean)
                }
            }

            shoppingGoldRechargeAdapter =
                ShoppingGoldRechargeAdapter(virtualGoldRechargeList as ArrayList)
            shoppingGoldRechargeAdapter?.onItemClickListener =
                { virtualGoldRechargeBean: VirtualGoldRechargeBean, isReadSelected: Boolean ->
                    if (isReadSelected) {
                        payVirtualGoldListener?.onSetCurrentRechargeItem(null)
                    } else {
                        payVirtualGoldListener?.onSetCurrentRechargeItem(virtualGoldRechargeBean)
                    }
                    if (cbVirtualMoneyOnOff.isChecked) {
                        setCbVirtualMoneyStatus(isSelect = true, needRequest = true)
                    } else {
                        setCbVirtualMoneyStatus(isSelect = true, needRequest = false)
                    }
                }
            rvShoppingGold.setAdapter(shoppingGoldRechargeAdapter)
        }
    }

    private fun setCbVirtualMoneyStatus(isSelect: Boolean, needRequest: Boolean) {
        cbVirtualMoneyOnOff.isChecked = isSelect
        if (needRequest) {
            payVirtualGoldListener?.onRefreshDateCallBack()
        }

    }
    /**
     * 判断购物金余额是否为0
     * @return true表示余额为0，false表示余额不为0
     */
    fun isAvailableVirtualGoldZero(): Boolean {
        val availableVirtualGold = mData?.availableVirtualGold ?: 0.0
        return availableVirtualGold <= 0.0
    }
    /**
     * 是否使用购物金
     * @return
     */
    fun isUseVirtualMoney(): Boolean {
        return isVisible && cbVirtualMoneyOnOff.isChecked
    }
}