package com.ybmmarketkotlin.views

import android.content.Context
import android.util.AttributeSet
import androidx.lifecycle.LifecycleOwner
import com.ybmmarketkotlin.bean.OrderTopBannerBean
import com.ybmmarketkotlin.views.OrderTopBannerLayout.OnBannerClickCallBack
import com.youth.banner.Banner
import com.youth.banner.listener.OnPageChangeListener

class BannerOrderTopView @JvmOverloads constructor(context: Context, attr: AttributeSet? = null) :
    Banner<OrderTopBannerBean, OrderTopBannerAdapter>(context, attr) {
    var adapter: OrderTopBannerAdapter? = null
    private var bannerClickCallBack: OnBannerClickCallBack? = null
    fun setBannerClickCallBack(listener: OnBannerClickCallBack) {
        this.bannerClickCallBack = listener
    }
    fun setData(dataList: List<OrderTopBannerBean>, curPageIndex: Int = 0) {
        adapter = OrderTopBannerAdapter(context, dataList)
        adapter!!.banner = this
        adapter!!.curPageIndex = curPageIndex
        adapter?.setBannerClickCallBack(bannerClickCallBack)
        if (context is LifecycleOwner) {
            addBannerLifecycleObserver(context as LifecycleOwner)
        }
        setAdapter(adapter, true)
        setLoopTime(3000L)
        setUserInputEnabled(true)
        addOnPageChangeListener(object : OnPageChangeListener {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
                adapter!!.curPageIndex = position
                bannerClickCallBack?.onBannerExposure(dataList.get(position).type)
            }

            override fun onPageScrollStateChanged(state: Int) {
            }
        })
        setCurrentItem(curPageIndex + 1, false)
    }
}