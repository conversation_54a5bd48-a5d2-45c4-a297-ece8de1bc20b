package com.ybmmarketkotlin.adapter

import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.adapter.YBMBaseListAdapter
import com.ybmmarket20.bean.GoodsSearchRecommendInfo
import com.ybmmarketkotlin.views.combinedbuy.CombinedBuyListener
import com.ybmmarketkotlin.views.search.SearchRecommendLayout

/**
 * <AUTHOR>
 * @desc    大搜推荐商品
 * @date    2025/8/18
 */
class GoodsSearchRecommendAdapter(recommendInfo: MutableList<GoodsSearchRecommendInfo>?) :
    YBMBaseListAdapter<GoodsSearchRecommendInfo>(R.layout.adapter_search_recommend, recommendInfo) {
    var mListener: CombinedBuyListener? = null
    var scrollPosition:Int = -1   // scroll item位置记录
    var scrollOffset : Int = 0
    var loadMoreFlag:Boolean = false
        set(value) {
            field = value
            if(!field){
                scrollPosition = -1
                scrollOffset = 0
            }
        }


    public override fun bindItemView(baseViewHolder: YBMBaseHolder, recommendInfo: GoodsSearchRecommendInfo) {
        val layoutRecomm = baseViewHolder.itemView.findViewById<SearchRecommendLayout>(R.id.layoutSearchRecommend)
        // 首次展示banner，不是viewHolder复用
        if(!loadMoreFlag && scrollPosition == -1){
            scrollPosition = 0
        }
        layoutRecomm.setNewData(recommendInfo,scrollPosition,scrollOffset)
        layoutRecomm.mListener = mListener
    }

    public override fun bindItemView(baseViewHolder: YBMBaseHolder, recommendInfo: GoodsSearchRecommendInfo, payloads: List<Any?>) {
        super.bindItemView(baseViewHolder, recommendInfo, payloads)
        payloads.forEach {
            it?:return@forEach
            val layoutRecomm = baseViewHolder.itemView.findViewById<SearchRecommendLayout>(R.id.layoutSearchRecommend)
            layoutRecomm.setNewData(recommendInfo,scrollPosition,scrollOffset)
        }
    }
}