package com.ybmmarketkotlin.adapter

import android.text.SpannableStringBuilder
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.xyy.canary.utils.DensityUtil
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.adapter.YBMBaseListAdapter
import com.ybmmarket20.bean.ActPtBean
import com.ybmmarket20.bean.ProductEditLayoutSuccessParams
import com.ybmmarket20.bean.RowsBeanCombinedExt
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.db.info.HandlerGoodsDao
import com.ybmmarket20.utils.ControlGoodsDialogUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpanUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.view.ProductEditLayout
import com.ybmmarket20.view.ProductEditLayoutNew
import com.ybmmarket20.view.ProductEditLayoutNew.BUTTON_TYPE_ADD
import com.ybmmarket20.view.ProductEditLayoutNew.BUTTON_TYPE_INPUT
import com.ybmmarketkotlin.adapter.goodslist.GoodListBindItemStatus
import com.ybmmarketkotlin.views.combinedbuy.CombinedBuyListener

/**
 * <AUTHOR>
 * @desc    大搜搜索推荐单个商品item
 * @date    2025/5/6
 */
open class GoodsSearchRecommendItemAdapter(products: MutableList<RowsBeanCombinedExt>?) :
    GoodsCombinedBuyItemAnalysisAdapter(R.layout.adapter_search_recommend_goods, products) {
    var mListener: CombinedBuyListener? = null

    override fun bindItemView(baseViewHolder: YBMBaseHolder, rowsBean: RowsBeanCombinedExt) {
        super.bindItemView(baseViewHolder, rowsBean)
        val ivPic = baseViewHolder.itemView.findViewById<ImageView>(R.id.ivPic)
        val tvGoodsName = baseViewHolder.itemView.findViewById<TextView>(R.id.tvProName)
        val tvTag = baseViewHolder.itemView.findViewById<TextView>(R.id.tvTag)
        ImageHelper.with(mContext).load(AppNetConfig.LORD_IMAGE + rowsBean.imageUrl)
            .placeholder(R.drawable.jiazaitu_min)
            .diskCacheStrategy(DiskCacheStrategy.SOURCE)
            .dontAnimate()
            .into(ivPic)
        // 营销标签
        if (rowsBean.marketingSlogan.isNullOrBlank()) {
            tvTag.visibility = View.GONE
        } else {
            tvTag.visibility = View.VISIBLE
            tvTag.text = rowsBean.marketingSlogan
        }
        tvGoodsName.text = rowsBean.showName
        baseViewHolder.itemView.setOnClickListener {
            mListener?.jumpToGoodsDetail(rowsBean)
        }
        showGoods(baseViewHolder, rowsBean)
    }

    /**
     * 展示未读数
     */
    private fun showCartNum(tvCartNum: TextView, rowsBean: RowsBeanCombinedExt) {
        if (rowsBean.newQty > 0) {
            tvCartNum.text = if (rowsBean.newQty > 99) {
                "99+"
            } else {
                "${rowsBean.newQty}"
            }
            tvCartNum.visibility = View.VISIBLE
        } else {
            tvCartNum.visibility = View.GONE
        }
    }

    private fun goneViews(vararg views: View) {
        views.forEach {
            it.visibility = View.GONE
        }
    }

    /**
     * 根据不同品，展示不同状态、按钮、价格
     */
    private fun showGoods(baseViewHolder: YBMBaseHolder, rowsBean: RowsBeanCombinedExt) {
        val tvGoodsPrice = baseViewHolder.itemView.findViewById<TextView>(R.id.tvProPrice)
        val tvCartNum = baseViewHolder.itemView.findViewById<TextView>(R.id.tvCartNum)
        val elEdit = baseViewHolder.itemView.findViewById<ProductEditLayoutNew>(R.id.elEdit)
        val tvJoinGroup = baseViewHolder.itemView.findViewById<RoundTextView>(R.id.tv_join_groupbooking)
        val tvSeckillCommit = baseViewHolder.itemView.findViewById<TextView>(R.id.tvSeckillCommit)
        // 给加购传入数据
        setProductEdit(elEdit, ProductEditLayout.FROMPAGE_ACTIVITS, rowsBean, baseViewHolder.bindingAdapterPosition, tvCartNum)
        // 隐藏不同type组件，对应类型显示
        goneViews(tvCartNum, elEdit, tvJoinGroup, tvSeckillCommit)
        var sbPrice = rowsBean.getShowPriceStrNew(false)
        val sbPriceStr = sbPrice.toString().replace("¥", "")
            .replace(rowsBean.pricePrefix ?: "", "")
            .replace("起", "").trim()
        tvGoodsPrice.setLineSpacing(0f, 0.75f)
        tvGoodsPrice.text = showPrice(baseViewHolder,rowsBean,sbPriceStr)
        if (rowsBean.isGroupBookingRow is GoodListBindItemStatus.SpellGroupStatusActive) {
            tvJoinGroup.visibility = View.VISIBLE
            tvJoinGroup.setOnClickListener {
                mListener?.btnClickTrack(1,tvJoinGroup.text.toString(),baseViewHolder.bindingAdapterPosition)
                if (rowsBean.isControlGoods) {
                    //控销品
                    ControlGoodsDialogUtil.showControlGoodsDialog(mContext, rowsBean)
                    return@setOnClickListener
                }
                if (rowsBean.actPt != null && rowsBean.actPt.isApplyListShowType) {
                    val mPopWindowSpellGroup = SpellGroupPopWindow(
                        baseViewHolder.itemView.context,
                        rowsBean,
                        rowsBean.actPt,
                        false,
                        mIsList = true,
                        null
                    )
                    mPopWindowSpellGroup.show(baseViewHolder.itemView)
                } else {
                    var mUrl = "ybmpage://productdetail/" + rowsBean.id
                    RoutersUtils.open(mUrl)
                }
            }
        } else if (rowsBean.isGroupBookingRow is GoodListBindItemStatus.SpellGroupStatusPreHot) {
            // 团购预热不做处理
        } else if (rowsBean.isGroupBookingRow is GoodListBindItemStatus.SpellGroupStatusSellOut) {
            // 售罄不做处理
        } else if (rowsBean.isGroupBookingRow is GoodListBindItemStatus.SeckillStatus) {
            elEdit.visibility = View.VISIBLE
            rowsBean.newQty = HandlerGoodsDao.getInstance().getNumber(rowsBean.id, false)
            showCartNum(tvCartNum, rowsBean)
            // 秒杀开始前、结束 不做处理rowsBean.actSk.status == 0,2
        } else if (rowsBean.isGroupBookingRow is GoodListBindItemStatus.PGBYStatus) {
            tvSeckillCommit.visibility = View.VISIBLE
            tvSeckillCommit.setOnClickListener {
                mListener?.btnClickTrack(2,tvSeckillCommit.text.toString(),baseViewHolder.bindingAdapterPosition)
                if (rowsBean.actPgby != null && rowsBean.actPgby.isApplyListShowType) {
                    val actPgby = rowsBean.actPgby
                    val actPt: ActPtBean = ActPtBean().apply {
                        assemblePrice = actPgby.assemblePrice ?: 0.0
                        skuStartNum = actPgby.skuStartNum?.toInt() ?: 0
                        marketingId = actPgby.marketingId
                        supportSuiXinPin = actPgby.supportSuiXinPin
                        suiXinPinButtonText = actPgby.suiXinPinButtonText
                        suiXinPinButtonBubbleText = actPgby.suiXinPinButtonBubbleText
                        isApplyListShowType = actPgby.isApplyListShowType
                    }
                    rowsBean.actPt = actPt

                    val mPopWindowSpellGroup = SpellGroupPopWindow(
                        baseViewHolder.itemView.context,
                        rowsBean,
                        actPt,
                        true,
                        mIsList = true,
                        null
                    )
                    rowsBean.actPt = null
                    mPopWindowSpellGroup.show(baseViewHolder.itemView)
                } else {
                    var mUrl = "ybmpage://productdetail/" + rowsBean.id
                    RoutersUtils.open(mUrl)
                }
            }
        } else {
            elEdit.visibility = View.VISIBLE
            rowsBean.newQty = HandlerGoodsDao.getInstance().getNumber(rowsBean.id, false)
            showCartNum(tvCartNum, rowsBean)
        }
    }

    /**
     * 调整价格、按钮大小，以适配多设备展示
     */
    private fun showPrice(baseViewHolder: YBMBaseHolder, rowsBean: RowsBeanCombinedExt, showPrice: String): SpannableStringBuilder {
        val tvGoodsPrice = baseViewHolder.itemView.findViewById<TextView>(R.id.tvProPrice)
        val tvJoinGroup = baseViewHolder.itemView.findViewById<RoundTextView>(R.id.tv_join_groupbooking)
        val tvSeckillCommit = baseViewHolder.itemView.findViewById<TextView>(R.id.tvSeckillCommit)
        tvJoinGroup.textSize = 12f
        tvSeckillCommit.textSize = 12f
        val priceSplit = UiUtils.transform(showPrice).split(".")
        var maxSize = 14f
        var minSize = 12f
        when {
            priceSplit[0].length > 4 -> {
                // 万以上
                if(rowsBean.isGroupBookingRow is GoodListBindItemStatus.SpellGroupStatusActive
                    || rowsBean.isGroupBookingRow is GoodListBindItemStatus.PGBYStatus){
                    tvJoinGroup.textSize = 9f
                    tvSeckillCommit.textSize = 9f
                }
                maxSize = 10f
                minSize = 9f
            }

            priceSplit[0].length > 3 -> {
                // 千以上
                if(rowsBean.isGroupBookingRow is GoodListBindItemStatus.SpellGroupStatusActive
                    || rowsBean.isGroupBookingRow is GoodListBindItemStatus.PGBYStatus){
                    tvJoinGroup.textSize = 10f
                    tvSeckillCommit.textSize = 10f
                    maxSize = 12f
                    minSize = 10f
                }else{
                    maxSize = 13f
                    minSize = 11f
                }
            }

            priceSplit[0].length > 2 -> {
                // 百以上
                if(rowsBean.isGroupBookingRow is GoodListBindItemStatus.SpellGroupStatusActive
                    || rowsBean.isGroupBookingRow is GoodListBindItemStatus.PGBYStatus){
                    maxSize = 12f
                    minSize = 10f
                }else{
                    maxSize = 14f
                    minSize = 12f
                }
            }

            else -> {
                maxSize = 14f
                minSize = 12f
            }
        }
        return SpanUtils()
            .apply {
                if (rowsBean.isGroupBookingRow is GoodListBindItemStatus.SeckillStatus) {
                    tvGoodsPrice.maxLines = 2
                    append("秒杀价\n")
                    setFontSize(ConvertUtils.sp2px(9f))
                } else {
                    tvGoodsPrice.maxLines = 1
                }
            }
            .append("¥")
            .setFontSize(DensityUtil.dip2px(YBMAppLike.getAppContext(), minSize))
            .append(priceSplit[0])
            .setFontSize(DensityUtil.dip2px(YBMAppLike.getAppContext(), maxSize))
            .append(".${priceSplit[1]}")
            .setFontSize(DensityUtil.dip2px(YBMAppLike.getAppContext(), minSize))
            .create()
    }

    private fun setProductEdit(
        editlayout: ProductEditLayoutNew,
        pageFrom: Int,
        rowsBean: RowsBeanCombinedExt,
        index: Int,
        tvCartNum: TextView
    ) {
        try {
            editlayout.rowsBeanPosition = index
        } catch (e: Exception) {
            e.printStackTrace()
        }
        editlayout.setKeepMin(true)
        editlayout.isAddCartShowPopupWindow = true
        // 店铺搜索页 普通商品也要和大搜页面一样弹窗处理
        editlayout.rowsBean = rowsBean
        editlayout.visibility = View.VISIBLE
        editlayout.mSource = rowsBean.sourceType
        editlayout.mIndex = index.toString()
        editlayout.setOnNumberListener { view, number ->
            //点击加购也认为是点击商品埋点
            mListener?.btnClickTrack(0,"加购",index)
        }
        editlayout.setOnDelProductListener { number ->
            // 从加购变为不加购
            rowsBean.newQty = 0
            showCartNum(tvCartNum, rowsBean)
        }
        editlayout.setOnAddCartListener(object : ProductEditLayoutNew.AddCartListener {
            override fun onPreAddCart(params: RequestParams): RequestParams {
                rowsBean.newQty = params.paramsMap["amount"]?.toIntOrNull() ?: 0
                showCartNum(tvCartNum, rowsBean)
                if (rowsBean.nsid != null) {
                    params.put("nsid", rowsBean.nsid)
                    params.put("sdata", rowsBean.sdata ?: "")
                }
                return params
            }

            override fun onAddCartSuccess(params: ProductEditLayoutSuccessParams) {
                if (params.addType == BUTTON_TYPE_ADD || params.addType == BUTTON_TYPE_INPUT) {
                    rowsBean.newQty = params.amount?.toIntOrNull() ?: 0
                    showCartNum(tvCartNum, rowsBean)
                    ToastUtils.showShort("加入购物车成功")
                }
            }
        })

        editlayout.bindData(
            rowsBean.id,
            rowsBean.status,
            true,
            pageFrom,
            null,
            true,
            rowsBean.stepNum,
            rowsBean.isSplit == 1
        )
        if (rowsBean.isControlGoods) {
            editlayout.visibility = View.VISIBLE
        } else {
            if (rowsBean.showPriceType() == 0 && !(rowsBean.status == 2 || rowsBean.status == 4 || rowsBean.availableQty <= 0)) {
                editlayout.visibility = View.VISIBLE
            } else {
                editlayout.visibility = View.GONE
            }
        }
    }
}
