package com.ybmmarketkotlin.adapter

import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.bean.RowsBeanCombinedExt
import com.ybmmarket20.bean.SEARCH_LIST_CARD_TYPE_GOODS
import com.ybmmarket20.bean.SEARCH_LIST_CARD_TYPE_OPERATION_POSITION_SINGLE_GOODS
import com.ybmmarket20.bean.SearchRowsBean
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.xyyreport.page.search.SearchProductReport
import com.ybmmarket20.xyyreport.paramsInfo.IRowsBeanInfo

/**
 * 商品列表埋点
 */
abstract class GoodsListAnalysisAdapter<T>(
    data: MutableList<SearchRowsBean>
) :
    YBMBaseMultiItemAdapter<SearchRowsBean>(data) {

    private val mCacheRecord = mutableSetOf<Int>()
    private var isSingleCombineCacheRecorded = false

    fun trackSingleCombineGoodsExposure(rowsBeanInfo: IRowsBeanInfo?, position: Int) {
        SearchProductReport.trackSearchGoodsExposure(mContext, rowsBeanInfo, position)
    }

//    /**
//     * 组合购主品曝光
//     */
//    fun trackMainSingleCombineGoodsExposure(rowsBeanInfo: IRowsBeanInfo?, position: Int) {
//        if (!isSingleCombineCacheRecorded) {
//            isSingleCombineCacheRecorded = true
//            trackSingleCombineGoodsExposure(rowsBeanInfo, position)
//        }
//    }
//
//    /**
//     * 组合购副品曝光
//     */
//    fun trackSubSingleCombineGoodsExposure(rowsBeanInfo: IRowsBeanInfo?, position: Int) {
//        trackSingleCombineGoodsExposure(rowsBeanInfo, position)
//    }

    override fun bindItemView(baseViewHolder: YBMBaseHolder, t: SearchRowsBean) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            if (!mCacheRecord.contains(holder.bindingAdapterPosition)) {
                mCacheRecord.add(holder.bindingAdapterPosition)
                if (bean.operationInfo != null && bean.operationInfo.products.size != 1) return@whenAllNotNull
                val goods = if (bean.operationInfo != null && bean.operationInfo.products.size == 1) {
                    bean.operationInfo.products[0].apply {
                        isOPSingleGoods = true
                        activityPageId = bean.operationInfo.activityPageId
                    }

                } else if(bean.groupPurchaseInfo != null || bean.additionalPurchaseInfo != null || bean.cardType == 5) {
                    return@whenAllNotNull
                } else bean.productInfo
                SearchProductReport.trackSearchGoodsExposure(
                    mContext,
                    goods,
                    holder.bindingAdapterPosition
                )
            }
        }
    }

    fun trackCombinationBtnClick(bean: RowsBeanCombinedExt, curSubPosition: Int, addFlag:Boolean, preNum: Int) {
        if(preNum == -1){
            if(addFlag){
                //加
                SearchProductReport.trackGroupCombinationBtnClickAdd(mContext, curSubPosition, bean)
            }else{
                //减
                SearchProductReport.trackGroupCombinationItemClickSubtract(mContext, curSubPosition, bean)
            }
        }else{
            // 数量点击
            SearchProductReport.trackGroupCombinationBtnClickAddCount(mContext, curSubPosition, bean, "$preNum")
        }
    }

    fun clearCacheRecord() {
        mCacheRecord.clear()
        isSingleCombineCacheRecorded = false
    }

}