package com.ybmmarket20.fragments

import android.os.Bundle
import com.ybmmarket20.bean.GroupPurchaseInfo
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.RowsBeanCombinedExt
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.xyyreport.page.commodity.CommodityDetailReport
import com.ybmmarket20.xyyreport.page.search.GoodsPlaceExposureRecord
import com.ybmmarket20.xyyreport.page.search.SearchProductReport
import com.ybmmarket20.xyyreport.paramsInfo.IRowsBeanInfo
import com.ybmmarket20.xyyreport.spm.SpmExtensionConstant
import com.ybmmarket20.xyyreport.spm.SpmUtil

abstract class CommodityAnalysisFragment: BaseFragment() {

    private var hotSellingGoodsCache= ArrayList<Int>()
    var qtSkuData: String? = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        context?.let {con->
            SpmUtil.checkAnalysisContext(con) {
                //保证组合购和加价购可以使用SearchProductReport
                it.putExtension(SpmExtensionConstant.EXTENSION_GOODS_SCM_ID, "")
            }
        }
        super.onCreate(savedInstanceState)
    }

    fun trackCommodityPv(skuId: String?) {
        CommodityDetailReport.trackCommodityPv(requireActivity(), skuId)
        trackPageWakeup()
    }

    private fun trackPageWakeup() {
        val shareSourceApp = notNullActivity.intent.getStringExtra("qtShareData")
        CommodityDetailReport.trackWakeupPageExposure(notNullActivity, shareSourceApp,qtSkuData)
    }

    fun markGroupCombinationGoods(groupPurchaseInfo: GroupPurchaseInfo?, isSingle: Boolean, scmId: String?) {
        groupPurchaseInfo?.mainProduct?.isSingleCombinationPurchase = isSingle
        groupPurchaseInfo?.mainProduct?.isMultipleCombinationPurchase = !isSingle
        groupPurchaseInfo?.mainProduct?.scmId = scmId
        groupPurchaseInfo?.subProducts?.forEach {
            it.isSingleCombinationPurchase = isSingle
            it.isMultipleCombinationPurchase = !isSingle
            it.scmId = scmId
        }
    }

    fun trackCombinationBtnClick(bean: RowsBeanCombinedExt, curSubPosition: Int, addFlag:Boolean, preNum: Int) {
        if(preNum == -1){
            if(addFlag){
                //加
                context?.let { SearchProductReport.trackGroupCombinationBtnClickAdd(it, curSubPosition, bean) }
            }else{
                //减
                context?.let { SearchProductReport.trackGroupCombinationItemClickSubtract(it, curSubPosition, bean) }
            }
        }else{
            // 数量点击
            context?.let { SearchProductReport.trackGroupCombinationBtnClickAddCount(it, curSubPosition, bean, "$preNum") }
        }
    }

    fun trackSingleCombineGoodsExposure(rowsBeanInfo: IRowsBeanInfo?, position: Int) {
        context?.let { SearchProductReport.trackSearchGoodsExposure(it, rowsBeanInfo, position) }
    }

    fun hotSellingGoodsBtnClick(
        rowsBeanInfo: RowsBean,
        position: Int,
        expId: String?,
        qtListData: String?,
        scmId: String?
    ) {
        CommodityDetailReport.trackHotSellingGoodsBtnClick(
            notNullActivity,
            position,
            rowsBeanInfo.id,
            rowsBeanInfo.productName,
            expId,
            qtListData,
            rowsBeanInfo.qtSkuData,
            scmId = scmId
        )
    }
    fun hotSellingGoodsExposure(
        rowsBeanInfo: RowsBean,
        position: Int,
        expId: String?,
        qtListData: String?,
        scmId: String?
    ) {
        if (!hotSellingGoodsCache.contains(position)) {
            hotSellingGoodsCache.add(position)
            CommodityDetailReport.trackHotSellingGoodsExposure(
                notNullActivity,
                position,
                rowsBeanInfo.id,
                rowsBeanInfo.productName,
                expId,
                qtListData,
                rowsBeanInfo.qtSkuData,
                scmId = scmId
            )
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        GoodsPlaceExposureRecord.release(context)
    }

}