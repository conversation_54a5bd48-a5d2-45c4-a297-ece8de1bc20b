package com.ybmmarket20.business.comment.event;

import static com.ybmmarket20.activity.afterSales.activity.CompanyAfterSalesTipsActivityKt.TIPS_TYPE_INVOICE;

import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.TextView;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.google.gson.Gson;
import com.luck.picture.lib.tools.DoubleUtils;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.common.SmartExecutorManager;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.OrderDetailActivity;
import com.ybmmarket20.activity.OrderProductPriceActivity;
import com.ybmmarket20.activity.RemindProgressActivity;
import com.ybmmarket20.bean.ApplyInvoiceCheckStatusBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CheckOrderDetailBean;
import com.ybmmarket20.bean.CheckOrderRowsBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.FlowDataAnalysisSId;
import com.ybmmarket20.bean.ImPackUrlBean;
import com.ybmmarket20.bean.OrderActionBean;
import com.ybmmarket20.bean.OrderBuyAgainProduct;
import com.ybmmarket20.bean.OrderStockState;
import com.ybmmarket20.bean.PayForAnotherBean;
import com.ybmmarket20.bean.RemindSubmitBean;
import com.ybmmarket20.business.comment.ui.CommentActivity;
import com.ybmmarket20.business.comment.ui.CommentDetailActivity;
import com.ybmmarket20.business.order.OrderBuyAgainManager;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.Abase;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.home.MainActivity;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.repertory.FlowDataAnalysisRepertoryKt;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.ShareBitmapUtils;
import com.ybmmarket20.utils.ShareUtil;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.YbmCommand;
import com.ybmmarket20.utils.analysis.AnalysisConst;
import com.ybmmarket20.utils.analysis.BaseFlowData;
import com.ybmmarket20.utils.analysis.FlowDataAnalysisManagerKt;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.BuyAgainCantBuyBottomDialog;
import com.ybmmarket20.view.BuyAgainCantBuyCenterDialog;
import com.ybmmarket20.view.RefundOrCancelOrderOptimizePopWindow;
import com.ybmmarket20.view.RemindSubmitSuccessDialog;
import com.ybmmarket20.viewmodel.OrderDetailViewModel;
import com.ybmmarket20.viewmodel.RemindSubmitViewModel;
import com.ybmmarket20.xyyreport.page.orderList.OrderListReport;
import com.ybmmarket20.xyyreport.paramsInfo.IOrderListItem;
import com.ybmmarketkotlin.utils.RouterJump;
import com.ybmmarketkotlin.views.PayForAnotherSharePopWindow;
import com.ybmmarketkotlin.views.PayForAnotherSharePopWindowKt;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * <br>
 * 作者：zhuruqiao
 * 时间：2018/10/9 09:00
 * 邮箱：<EMAIL>
 */
public class OrderActionLayoutListener implements View.OnClickListener {


    public static final String TYPE_BUY_AGAIN = "再次购买";

    public static final String TYPE_ORDER_CANCEL = "取消订单";
    public static final String TYPE_ORDER_REFUND_GOODS = "申请退货";
    public static final String TYPE_ORDER_REFUND_MONEY = "申请退款";
    public static final String TYPE_ORDER_APPLY_AFTER_SALE = "申请售后";
    public static final String TYPE_ORDER_RECEIVED = "确认收货";
    public static final String TYPE_CHECK_REFUND = "退款/售后";
    public static final String TYPE_PAY_NOW = "立即支付";
    public static final String TYPE_GET_BALANCE = "领取余额";
    public static final String TYPE_VIEW_BALANCE = "查看余额";
    public static final String TYPE_COMMENT = "评价";
    public static final String TYPE_VIEW_COMMENT = "查看评价";
    public static final String TYPE_VIEW_STORE_PRICE = "查看入库价";
    public static final String TYPE_PAY_FOR_ANOTHER = "找人代付";
    public static final String TYPE_UPLOAD_ELEC_CERTIFICATE = "上传凭证";
    public static final String TYPE_VIEW_ELEC_CERTIFICATE = "查看凭证";
    public static final String TYPE_REMIND_ORDER = "提醒发货";
    public static final String TYPE_REMIND_ORDER_PROGRESS = "提醒发货进度";
    public static final String TYPE_VIEW_INVOICE = "查看发票";
    public static final String TYPE_VIEW_APPLY_INVOICE = "申请发票";
    private OrderActionBean bean;
    private BaseFlowData flowData;
    private boolean isList;
    public static boolean isList1;
    public IOrderListItem mIOrderListItem;
    public int mPosition;

    public void setOrderActionBean(OrderActionBean bean) {
        this.bean = bean;
    }

    /**
     * 设置埋点数据
     */
    public void setFlowData(BaseFlowData flowData) {
        this.flowData = flowData;
    }

    @Override
    public void onClick(View v) {
        if (bean == null) {
            return;
        }
        if (DoubleUtils.isFastDoubleClick()) {
            return;
        }
        if (v instanceof TextView) {

            String actionType = ((TextView) v).getText().toString();
            if (!TextUtils.isEmpty(actionType)) {

                if (actionType.contains(TYPE_PAY_NOW) || actionType.contains("去支付")){ //存在倒计时文案
                    if (bean.paytype != 1) {
                        OrderBuyAgainManager.Companion.buyAgain(((TextView) v),bean);
                    } else {
                        pay(bean, isList);
                    }
                }else if (actionType.contains(TYPE_VIEW_ELEC_CERTIFICATE) || actionType.contains(TYPE_UPLOAD_ELEC_CERTIFICATE)){ //存在倒计时文案
                    RoutersUtils.open(bean.transferInfoUrl);
                }else {
                    switch (actionType) {
                        case TYPE_BUY_AGAIN:
                            if (mIOrderListItem != null) {
                                OrderListReport.trackOrderItemBuyAgainBtnClick(getMContext(), mIOrderListItem, mPosition);
                            }
                            OrderBuyAgainManager.Companion.buyAgain(((TextView) v),bean);
                            break;
                        case TYPE_ORDER_CANCEL:
                            cancelOrder(v, bean.orderNo);
                            break;
                        case TYPE_ORDER_REFUND_GOODS:
                            applyRefund();
                            break;
                        case TYPE_ORDER_REFUND_MONEY:
                            //申请退款
                            applyRefund();
                            break;
                        case TYPE_ORDER_APPLY_AFTER_SALE:
                            //申请售后
                            applyAfterSale();
                            break;
                        case TYPE_VIEW_BALANCE:
                            viewBalance();
                            break;
                        case TYPE_ORDER_RECEIVED:
                            confirmOrder();
                            break;
                        case TYPE_CHECK_REFUND:
                            viewRefund();
                            break;
//                    case TYPE_PAY_NOW:
//                        if (bean.paytype != 1) {
//                            OrderBuyAgainManager.Companion.buyAgain(((TextView) v),bean);
//                        } else {
//                            pay(bean, isList);
//                        }
//                        break;
                        case TYPE_GET_BALANCE:
                            getBalance();
                            break;
                        case TYPE_COMMENT:
                            launchComment();
                            break;
                        case TYPE_VIEW_COMMENT:
                            launchCommentDetail();
                            break;
                        case TYPE_VIEW_STORE_PRICE:
                            orderPriceList();
                            break;
                        case TYPE_PAY_FOR_ANOTHER://找人代付
                            getShareCount(v, bean.orderNo);
                            break;
//                    case  TYPE_VIEW_ELEC_CERTIFICATE:  //查看电汇凭证
//                    case TYPE_UPLOAD_ELEC_CERTIFICATE: // 上传电汇凭证
//                        RoutersUtils.open(bean.transferInfoUrl);
//                        break;
                        case TYPE_REMIND_ORDER:
                            // 提醒发货
                            initObserver(v);
                            break;
                        case TYPE_REMIND_ORDER_PROGRESS:
                            // 提醒发货进度
                            Intent intent = new Intent(getMContext(), RemindProgressActivity.class);
                            intent.putExtra(IntentCanst.ORDER_NO, bean.orderNo + "");
                            getMContext().startActivity(intent);
                            break;
                        case TYPE_VIEW_INVOICE:
                            getOrderDetail(bean.id,"1");
                            break;
                        case TYPE_VIEW_APPLY_INVOICE:
                            applyInvoicePre();
                            break;

                    }
                }

            }
        }
    }

    /**
     * @desc    获取发票详情、查看发票(invoiceType == 1)
     * @param invoiceType "1"：查看发票 "": 获取订单详情
     */
    private void getOrderDetail(String orderId,String invoiceType){
        //查看订单详情
        showProgress();
        // 查看发票
        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        if (!TextUtils.isEmpty(orderId)) {
            params.put("id", orderId);
        }
        params.put("sceneType", "1");
        HttpManager.getInstance().post(AppNetConfig.ORDER_DETAIL, params, new BaseResponse<CheckOrderDetailBean>() {

            @Override
            public void onSuccess(String content, BaseBean<CheckOrderDetailBean> listBean, CheckOrderDetailBean data) {
                dismissProgress();
                if (listBean != null && listBean.isSuccess() && data != null) {
                    if (listBean.isSuccess()) {
                        if(invoiceType.equals("1")){
                            RouterJump.INSTANCE.jump2InvoiceList(data);
                        }
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }
        });
    }

    /**
     * @desc 发票申请：申请前状态判断
     */
    private void applyInvoicePre(){
        //申请发票
        showProgress();
        RemindSubmitViewModel viewModel = new ViewModelProvider(((BaseActivity) BaseYBMApp.getApp().getCurrActivity())).get(RemindSubmitViewModel.class);
        Observer<BaseBean<ApplyInvoiceCheckStatusBean>> observer = voiceData -> {
            dismissProgress();
            if (voiceData != null && voiceData.isSuccess() && voiceData.data != null) {
                if(voiceData.data.getCount() > 0){
                    //跳转售后详情: 原生 / H5
                    if(TextUtils.isEmpty(voiceData.data.getAfterSaleDetailHtmlUrl())){
                        RoutersUtils.open("ybmpage://aftersalesdetail?afterSalesNo="+voiceData.data.getAfterSalesNo());
                    }else{
                        RoutersUtils.open("ybmpage://commonh5activity?url=" + voiceData.data.getAfterSaleDetailHtmlUrl() + "&isShowCart=0");
                    }
                }else{
                    //申请发票
                    applyInvoice(bean.orderNo,bean.orgId,bean.companyName,bean.getApplyInvoiceHtmlUrl(),bean.getApplyInvoiceAfterSaleHtmlUrl());
                }
            }else {
                // 申请发票
                applyInvoice(bean.orderNo,bean.orgId,bean.companyName,bean.getApplyInvoiceHtmlUrl(),bean.getApplyInvoiceAfterSaleHtmlUrl());
            }
        };
        // 发票数量查询
        viewModel.queryInvoiceAfterSalesCount(bean.orderNo).observe(((BaseActivity) BaseYBMApp.getApp().getCurrActivity()), observer);
    }

    private void getShareCount(View v, String orderNo) {//他人代付 获取支付链接信息
        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());
        params.put("orderNo", orderNo);
        HttpManager.getInstance().post(AppNetConfig.PAY_FOR_ANOTHER_GET_SHARE_URL_LIST, params, new BaseResponse<List<PayForAnotherBean>>() {
            @Override
            public void onSuccess(String content, BaseBean<List<PayForAnotherBean>> obj, List<PayForAnotherBean> data) {
                if (obj != null && obj.isSuccess() && data != null) {
                    new PayForAnotherSharePopWindow(data, bean -> {
                        ShareBitmapUtils.PayAnotherShareData payAnotherShareData = new ShareBitmapUtils.PayAnotherShareData();
                        if (bean != null) {
                            payAnotherShareData.shopName = bean.getShopName();
                            payAnotherShareData.payAmountDesc = bean.getPayAmountDesc();
                            payAnotherShareData.createTimeDesc = bean.getCreateTimeDesc();
                        }
                        switch (bean.getChannelType()) {
                            case PayForAnotherSharePopWindowKt.SHARED_TYPE_COPY://复制链接
                                YbmCommand.setClipboardMsg(bean.getShareUrl());
                                ToastUtils.showShort(getMContext().getString(R.string.str_pay_for_another_copy_tips));
                                break;
                            case PayForAnotherSharePopWindowKt.SHARED_TYPE_WX://分享微信朋友
                                SmartExecutorManager.getInstance().execute(() -> {
                                    Bitmap bitmap = ShareBitmapUtils.getShareBitmap(getMContext(), payAnotherShareData);
                                    if (bitmap == null) {
                                        try {
                                            bitmap = ImageHelper.with(getMContext()).load(bean.getImageUrl()).asBitmap().placeholder(R.color.white).dontAnimate().dontTransform().diskCacheStrategy(DiskCacheStrategy.SOURCE).into(100, 100).get();
                                        }catch (Exception e) {
                                            bitmap = BitmapFactory.decodeResource(Abase.getResources(), R.drawable.logo);
                                        }
                                    }
                                    if(bean.isMiniProg() == 1) {
                                        //微信小程序
                                        ShareUtil.shareWXMiniProg(bean.getTitle(), bean.getShareUrl(), bean.getPath(), bean.getAppId(), bean.getDesc(), bitmap, false);
                                    }else {
                                        ShareUtil.shareWXPage(bean.getTitle(), bean.getShareUrl(), bean.getDesc(), bitmap);
                                    }
                                });
                                break;
                            case PayForAnotherSharePopWindowKt.SHARED_TYPE_AP: //分享到支付宝好友
                                SmartExecutorManager.getInstance().execute(() -> {
                                    try {
                                        ShareUtil.shareAliPayPage(BaseYBMApp.getAppContext(), bean.getTitle(), bean.getShareUrl(), bean.getDesc(), bean.getImageUrl());
                                    } catch (Exception e) {
//                                        e.printStackTrace()
                                    }
                                });
                                break;
                        }
                    }).show(v);
                }
            }

            @Override
            public void onFailure(NetError error) {
                ToastUtils.showShort("获取订单支付链接失败，请重试");
            }
        });
    }

    //去支付
    private static void pay(OrderActionBean bean, boolean isList) {
        RoutersUtils.open("ybmpage://paywayactivity?orderId=" + bean.id + "&amount=" + bean.money + "&payRoute=1" + "&orderNo=" + bean.orderNo);
    }

    /**
     * 确认收货
     */
    private void confirmOrder() {
        String ReceiptText = "请收到商品后，再确认收货，否则您将可能钱货两空。";
        final AlertDialogEx alert = new AlertDialogEx(getMContext());
        alert.setMessage(ReceiptText);
        alert.setCancelButton("我再想想", null);
        alert.setConfirmButton("确认收货", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                showProgress();
                RequestParams params = new RequestParams();
                params.put("merchantId", SpUtil.getMerchantid());
                params.put("orderId", bean.id);
                HttpManager.getInstance().post(AppNetConfig.ORDER_RECEIPT, params, new BaseResponse<EmptyBean>() {
                    @Override
                    public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                        dismissProgress();
                        if (obj != null && obj.isSuccess()) {
                            bean.canConfirmReceipt = 0;
                            Intent intent = new Intent(IntentCanst.ACTION_ORDER_LIST_REFRESH);
                            intent.putExtra("bean", bean);
                            LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(intent);
                            return;
                        }
                    }

                    @Override
                    public void onFailure(NetError error) {
                        dismissProgress();
                        ToastUtils.showShort(error.message);
                        if (bean.result != null) {
                        }
                    }

                });
            }
        });
        alert.show();
    }

    private void initObserver(View v) {
        try {
            RemindSubmitViewModel viewModel = new ViewModelProvider(((BaseActivity) BaseYBMApp.getApp().getCurrActivity())).get(RemindSubmitViewModel.class);
            Observer<BaseBean<RemindSubmitBean>> observer = data -> {
                if (data != null && data.isSuccess()) {
                    RemindSubmitSuccessDialog bottomPopWindow = new RemindSubmitSuccessDialog(getMContext(), data.data.getMsg());
                    bottomPopWindow.setRemindSubmitSuccessListener(new RemindSubmitSuccessDialog.RemindSubmitSuccessListener() {
                        @Override
                        public void goOnLineService() {
                            sendOnLineService();
                            bottomPopWindow.dismiss();
                        }
                    });
                    bottomPopWindow.show();
                    bottomPopWindow.setOnDismissListener(new DialogInterface.OnDismissListener() {
                        @Override
                        public void onDismiss(DialogInterface dialog) {
                            OrderDetailActivity currActivity = (OrderDetailActivity) BaseYBMApp.getApp().getCurrActivity();
                            currActivity.refreshData();
                        }
                    });
                }else {
                    OrderDetailActivity currActivity = (OrderDetailActivity) BaseYBMApp.getApp().getCurrActivity();
                    currActivity.refreshData();
                }
            };
            viewModel.getRemindSubmit(bean.orderNo).observe(((BaseActivity) BaseYBMApp.getApp().getCurrActivity()), observer);
        } catch (Exception e) {
        }
    }
    //申请发票售后
    private void applyInvoice(String mOrderNo, String mOrgId, String mOrgName,String applyInvoiceHtmlUrl,String applyInvoiceAfterSaleHtmlUrl) {
        try {
            OrderDetailViewModel viewModel = new ViewModelProvider(((BaseActivity) BaseYBMApp.getApp().getCurrActivity())).get(OrderDetailViewModel.class);
            CheckOrderDetailBean orderDetail = new CheckOrderDetailBean();
            orderDetail.orderNo = mOrderNo;
            orderDetail.orgId = mOrgId;
            orderDetail.origName = mOrgName;

            Observer<String> observer1 = data -> {
                if (data != null) {
                    RoutersUtils.open(data);
                } else {
                    OrderDetailActivity currActivity = (OrderDetailActivity) BaseYBMApp.getApp().getCurrActivity();
                    currActivity.refreshData();
                }
            };
            viewModel.getAfterSalesInfo(orderDetail, TIPS_TYPE_INVOICE, applyInvoiceHtmlUrl,applyInvoiceAfterSaleHtmlUrl)
                    .observe(((BaseActivity) BaseYBMApp.getApp().getCurrActivity()), observer1);
        } catch (Exception e) {
        }
    }

    //查看余额
    private static void viewBalance() {
        RoutersUtils.open("ybmpage://balanceactivity/");
    }

    /**
     * 领取余额
     */
    private void getBalance() {
        if (bean == null) {
            return;
        }
        if (TextUtils.isEmpty(bean.balanceText)) {
            bean.balanceText = "确认收货后，可领取余额，同时此订单将无法申请退款";
        }
        final AlertDialogEx alert = new AlertDialogEx(getMContext());
        alert.setMessage(bean.balanceText);
        alert.setConfirmButton("同意并领取", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                showProgress();
                RequestParams params = new RequestParams();
                params.put("merchantId", SpUtil.getMerchantid());
                params.put("orderId", bean.id);
                HttpManager.getInstance().post(AppNetConfig.ORDERS_BALANCE, params, new BaseResponse<EmptyBean>() {
                    @Override
                    public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                        dismissProgress();
                        if (obj != null && obj.isSuccess()) {
                            ToastUtils.showShort("领取余额成功");
                            bean.balanceStatus = 1;
                            Intent intent = new Intent(IntentCanst.ACTION_ORDER_LIST_REFRESH);
                            intent.putExtra("bean", bean);
                            LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(intent);
                            return;
                        }
                    }

                    @Override
                    public void onFailure(NetError error) {
                        dismissProgress();
                    }

                });
            }
        });
        alert.setCancelButton("暂不领取", null);
        alert.show();
    }


    //查看退款
    public void viewRefund() {
//        RoutersUtils.open("ybmpage://refundlist/" + bean.id);
        RoutersUtils.open("ybmpage://refundoraftersales?orderNo=" + bean.orderNo);
    }

    //取消订单
    private void cancelOrder(View v, String orderNo) {//取消订单这里 订单状态默认传4
        RefundOrCancelOrderOptimizePopWindow popWindow = new RefundOrCancelOrderOptimizePopWindow(orderNo, "4", "取消订单原因");
        popWindow.setSelectOptimizeListener(reasonBean -> {//选择取消订单原因
            String reasonStr = reasonBean.getShowText();
            postCancelOrderApi(reasonStr);
        });
        popWindow.show(v);
    }

    private void postCancelOrderApi(String reasonStr) {
        showProgress();
        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        params.put("id", String.valueOf(bean.id));
        params.put("cancelReason", reasonStr);
        HttpManager.getInstance().post(AppNetConfig.ORDERS_CANCEL, params, new BaseResponse<EmptyBean>() {
            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                if (BaseYBMApp.getApp().getCurrActivity() != null) {
                    ((BaseActivity) BaseYBMApp.getApp().getCurrActivity()).dismissProgress();
                }
                if (obj != null) {
                    if (obj.isSuccess()) {
                        if (isList) {
                            Intent intent = new Intent(IntentCanst.ACTION_ORDER_LIST_REFRESH);
                            intent.putExtra("bean", bean);
                            LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(intent);
                        } else {
                            if (BaseYBMApp.getApp().getCurrActivity() != null) {
                                Intent intent = new Intent(IntentCanst.ACTION_ORDER_LIST_REFRESH);
                                intent.putExtra("bean", bean);
                                LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(intent);
                                JSONObject jsonObject = new JSONObject();
                                try {
                                    jsonObject.put("orderId", bean.id);
                                    jsonObject.put("orderNo", bean.orderNo);
                                    jsonObject.put("orderStatus", 1);
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }
                                XyyIoUtil.track(XyyIoUtil.ACTION_CANCEL_ORDER, jsonObject);
                                ((BaseActivity) BaseYBMApp.getApp().getCurrActivity()).finish();
                            }
                        }
                        if (!TextUtils.isEmpty(obj.msg)) {
                            ToastUtils.showShort(obj.msg);
                        }
                        return;
                    } else {
                        JSONObject jsonObject = new JSONObject();
                        try {
                            jsonObject.put("orderId", bean.id);
                            jsonObject.put("orderNo", bean.orderNo);
                            jsonObject.put("orderStatus", 2);
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        XyyIoUtil.track(XyyIoUtil.ACTION_CANCEL_ORDER, jsonObject);
                    }
                }

            }

            @Override
            public void onFailure(NetError error) {
                if (BaseYBMApp.getApp().getCurrActivity() != null) {
                    ((BaseActivity) BaseYBMApp.getApp().getCurrActivity()).dismissProgress();
                }

            }
        });
    }


    private void showProgress() {
        if (BaseYBMApp.getApp().getCurrActivity() != null) {
            ((BaseActivity) BaseYBMApp.getApp().getCurrActivity()).showProgress();
        }
    }

    private Context getMContext() {
        if (BaseYBMApp.getApp().getCurrActivity() != null) {
            return BaseYBMApp.getApp().getCurrActivity();
        }
        return BaseYBMApp.getAppContext();
    }

    private void dismissProgress() {
        if (BaseYBMApp.getApp().getCurrActivity() != null) {
            ((BaseActivity) BaseYBMApp.getApp().getCurrActivity()).dismissProgress();
        }
    }

    private void finish() {
        if (YBMAppLike.getApp().getCurrActivity() != null && YBMAppLike.getApp().getCurrActivity() instanceof BaseActivity) {
            YBMAppLike.getApp().getCurrActivity().finish();
        }
//        if (BaseYBMApp.getApp().getCurrActivity() != null) {
//            BaseYBMApp.getApp().getCurrActivity().finish();
//        }
    }

    /**
     * 去评价
     */
    private void launchComment() {
        CommentActivity.launch(getMContext(), bean.orderNo);

    }

    /**
     * 查看评价详情
     */
    private void launchCommentDetail() {
        CommentDetailActivity.launch(getMContext(), bean.orderNo);

    }

    //去入库价格明细
    private void orderPriceList() {
        OrderProductPriceActivity.getIntent2Me(getMContext(), bean.rowsBeans, bean.id);
    }

    //申请售后
    private void applyAfterSale() {
        if (bean.getApplyAfterSaleHtmlUrl().isEmpty()) {
            RoutersUtils.open("ybmpage://selectservicetype/" + bean.status + "/" + bean.orderNo + "/" + bean.id + "/" + bean.companyName);
        } else {
            skipCommonH5();
        }
    }

    //申请退款
    private void applyRefund() {
        if (bean == null) {
            return;
        }


        int payType = bean.paytype;
        if (payType == 1 || payType == 3) {
            if (bean.balanceStatus == 1) {
                if (TextUtils.isEmpty(bean.refundText)) {
                    bean.refundText = "已经确认收货并领取余额，无法申请售后";
                }
                AlertDialogEx alert = new AlertDialogEx(getMContext());
                alert.setMessage(bean.refundText);
                alert.setCancelButton("我知道了", null);
                alert.show();
            } else {
                if (bean.status == 7 && bean.isThirdCompany != 1) {  // 出库中的自营(pop可部分退)订单只能整单退，先查询是否提交过退款申请，如果提交过则直接拦截
                    queryIfApplyRefund();
                } else {  // 非出库中的订单可以选择部分商品退
                    refundPart();
                }

            }
        } else {
            ToastUtils.showShort("货到付款的订单退款请联系客服");
        }
    }
    private void skipCommonH5() {
        RoutersUtils.open("ybmpage://commonh5activity?url=" + bean.getApplyAfterSaleHtmlUrl() + "&isShowCart=0");
    }
    private void refundPart() {
        // 1、2、10   没有完成的订单,退款
        String preRouterStr = "ybmpage://choiceproduct/" + bean.status + "/" + bean.orderNo + "/";
            if (bean.status == 10) { // 待支付订单 不需要退款提示
                if (bean.getApplyAfterSaleHtmlUrl().isEmpty()) {
                    RoutersUtils.open(preRouterStr + bean.id);
                } else {
                    skipCommonH5();
                }
            } else {
                // 可部分退款的订单状态（除待支付订单）客户发起申请退款时，页面增加温馨提示
                // add 点击“申请退款”按钮，底部弹出的提示文案，挪到选择退款商品后点击“确认退款”时弹出；
                // 一天天就搞些没用的
                if (bean.status == 2 || bean.status == 3) {
                    // 配送中和已完成
                    if (bean.getApplyAfterSaleHtmlUrl().isEmpty()) {
                        RoutersUtils.open("ybmpage://selectservicetype/" + bean.status + "/" + bean.orderNo + "/" + bean.id + "/" + bean.companyName);
                    } else {
                        skipCommonH5();
                    }
                } else {
                    if (bean.getApplyAfterSaleHtmlUrl().isEmpty()) {
                        RoutersUtils.open(preRouterStr + bean.id + "/1");
                    } else {
                        skipCommonH5();
                    }
                }
        }
    }

    /**
     * 查询提交退款状态
     */
    private void queryIfApplyRefund() {
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.CANREFUND).addParam("orderId", bean.id).build();
        HttpManager.getInstance().post(params, new BaseResponse<EmptyBean>() {
            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> data, EmptyBean baseBean) {
                if (data.isSuccess()) {
                    openApplyRefund();
                }
            }

            @Override
            public void onFailure(NetError error) {
            }
        });
    }

    /**
     * 直接跳转至提交退款页面
     */
    private void openApplyRefund() {
        AlertDialogEx refundDialog = new AlertDialogEx(getMContext())
                .setMessage("您发起的退款订单目前只支持整单退款，订单中的相关优惠活动将在退款发起后无法退还，请您确认是否提交退款。\n" +
                        "注：目前您的订单已在仓库拣货打包中，拦截订单有风险，若拦截失败，订单会正常发货，请您随时关注订单拦截情况，若有疑问可联系客服。")
                .setMessageGravity(Gravity.LEFT)
                .setCancelButton("取消", null)
                .setConfirmButton("确认", new AlertDialogEx.OnClickListener() {
                    @Override
                    public void onClick(AlertDialogEx dialog, int button) {
                        if (bean.getApplyAfterSaleHtmlUrl().isEmpty()){
                            RoutersUtils.open("ybmpage://applyrefund/2/" + bean.id + "?status=" + bean.status + "&orderNo=" + bean.orderNo);
                        }else {
                            skipCommonH5();
                        }
                    }
                });
        refundDialog.show();
    }

    /*
     * 在线客服
     * */
    private void sendOnLineService() {

        RequestParams params = new RequestParams();
        params.put("isThirdCompany", bean.isThirdCompany + "");

        HttpManager.getInstance().post(AppNetConfig.GET_IM_PACKURL, params, new BaseResponse<ImPackUrlBean>() {

            @Override
            public void onSuccess(String content, BaseBean<ImPackUrlBean> obj, ImPackUrlBean baseBean) {
                if (obj != null && obj.isSuccess()) {
                    if (baseBean != null) {
                        RoutersUtils.open(RoutersUtils.getRouterPopCustomerServiceUrl(baseBean.IM_PACK_URL, bean.orgId, bean.orderNo, bean.companyName));
                    }
                }
            }

        });

    }
}
